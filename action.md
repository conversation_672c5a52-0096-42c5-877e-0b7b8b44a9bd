好的，我们现在来开发“阶段一：核心体验强化与沉浸式基础”的下一个部分：“**洞府装修（基础）**”。

这个迭代将允许用户为他们的“我的工位”（个人洞府）选择不同的背景主题，并持久化这些选择。

---

### 阶段一：核心体验强化与沉浸式基础

#### 5. 洞府装修（基础）

**改动点概述：**

1.  **后端 `backend/src/controllers/auth.controller.js`：** 修改 `updateProfile` 控制器函数，使其能正确处理 `preferences` 对象的嵌套更新。
2.  **前端 `frontend/src/views/Workspace.vue`：**
    *   定义并显示可供选择的洞府主题列表。
    *   添加主题选择的 UI 元素（例如单选按钮或下拉框）。
    *   实现选择主题后保存到用户偏好设置的逻辑。
    *   根据用户当前选择的主题，动态应用 CSS 类到页面容器上。
3.  **前端 `frontend/src/styles/ancient-pixel.scss`：** 定义各个洞府主题的 CSS 样式，包括背景图、背景色等。

---

**详细代码修改：**

### **1. 后端修改**

**1.1. `backend/src/controllers/auth.controller.js`**

修改 `updateProfile` 函数以支持 `preferences` 字段的更新。

```javascript
// File path: backend/src/controllers/auth.controller.js
/**
 * 认证控制器
 * 处理用户认证相关的业务逻辑
 */

// ... (现有引入模块：User, jwt, bcrypt, multer, path)

/**
 * 用户注册
 * ... (signup 函数保持不变)
 */
const signup = async (req, res, next) => { /* ... */ };

/**
 * 用户登录
 * ... (login 函数保持不变)
 */
const login = async (req, res, next) => { /* ... */ };

/**
 * 用户登出
 * ... (logout 函数保持不变)
 */
const logout = (req, res) => { /* ... */ };

/**
 * 获取当前用户信息
 * ... (getCurrentUser 函数保持不变)
 */
const getCurrentUser = async (req, res, next) => { /* ... */ };

/**
 * 刷新 Token
 * ... (refreshToken 函数保持不变)
 */
const refreshToken = async (req, res, next) => { /* ... */ };

// ... (Multer 配置和 uploadAvatar 保持不变)
const upload = multer({ /* ... */ });
const uploadAvatar = async (req, res, next) => { /* ... */ };


/**
 * 更新用户资料
 * @param {Object} req - Express 请求对象
 * @param {Object} res - Express 响应对象
 * @param {Function} next - Express 下一个中间件函数
 */
const updateProfile = async (req, res, next) => {
  try {
    const userId = req.user.id;
    // **修改：从请求体中解构出 preferences**
    const { nickname, position, signature, preferences } = req.body;

    const user = await User.findById(userId);

    if (!user) {
      console.log(`[auth.controller.js] updateProfile: 用户ID ${userId} 不存在`);
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 更新允许修改的字段
    if (nickname !== undefined) user.nickname = nickname;
    if (position !== undefined) user.position = position;
    if (signature !== undefined) user.signature = signature;

    // **新增：处理 preferences 对象的更新**
    if (preferences && typeof preferences === 'object') {
      // 合并新的偏好设置到现有设置中，避免覆盖未提供的字段
      // 注意：Mongoose schema 默认会处理子文档的保存，Object.assign 是一个浅拷贝
      // 对于简单的键值对更新是足够的。如果 preferences 下有更复杂的子文档，可能需要更深入的合并策略
      Object.assign(user.preferences, preferences);
    }

    const updatedUser = await user.save();

    console.log(`[auth.controller.js] 用户 ${userId} 资料更新成功`);
    res.json({
      success: true,
      message: '用户资料更新成功',
      user: updatedUser
    });

  } catch (error) {
    console.error('[auth.controller.js] 更新用户资料时发生错误:', error);
    if (error.name === 'ValidationError') {
        const errors = Object.values(error.errors).map(err => err.message);
        return res.status(400).json({
            success: false,
            message: errors.join(', ')
        });
    }
    next(error);
  }
};


// 导出控制器函数和可能的其他模块
module.exports = {
  signup,
  login,
  logout,
  getCurrentUser,
  refreshToken,
  uploadAvatar,
  upload,
  updateProfile,
};
```

### **2. 前端修改**

**2.1. `frontend/src/views/Workspace.vue`**

添加主题选择 UI 和逻辑，并动态应用主题类。

```vue
<script setup>
// File path: frontend/src/views/Workspace.vue
import { ref, onMounted, computed, watch } from 'vue';
import axios from 'axios';
import { useUserStore } from '@/stores/user';

const userStore = useUserStore();

const userData = computed(() => userStore.userInfo);
const loading = computed(() => userStore.loading);
const error = computed(() => userStore.error);
const fullAvatarUrl = computed(() => userStore.fullAvatarUrl);

const selectedFile = ref(null);
const uploading = ref(false);
const uploadSuccess = ref(false);
const uploadError = ref(null);

const editingProfile = ref(false);
const editableNickname = ref('');
const editablePosition = ref('');
const editableSignature = ref('');
const profileUpdateError = ref(null);
const profileUpdateSuccess = ref(false);

// **新增：洞府主题相关**
const availableThemes = ref([
  { id: 'default', name: '云雾缭绕', icon: '☁️' },
  { id: 'forest', name: '翠竹幽林', icon: '🌳' },
  { id: 'cave', name: '石窟洞府', icon: '🪨' },
  { id: 'ancient-hall', name: '殿宇楼阁', icon: '🏛️' }
]);
const selectedThemeId = ref('default'); // 当前选择的主题ID

// 监听 userData 变化，初始化编辑字段和主题
watch(userData, (newVal) => {
  if (newVal) {
    editableNickname.value = newVal.nickname || '';
    editablePosition.value = newVal.position || '';
    editableSignature.value = newVal.signature || '';
    // **新增：初始化主题**
    selectedThemeId.value = newVal.preferences?.theme || 'default';
  }
}, { immediate: true });

// Computed property to apply theme class
const currentThemeClass = computed(() => {
  return `theme-${selectedThemeId.value}`;
});


const handleFileChange = (event) => {
  const file = event.target.files[0];
  if (file) {
    selectedFile.value = file;
    uploadSuccess.value = false;
    uploadError.value = null;
  }
};

const triggerFileInput = () => {
  const fileInput = document.getElementById('avatarFile');
  if (fileInput) {
    fileInput.click();
  }
};

const uploadAvatar = async () => { /* ... (保持不变) ... */
  uploading.value = true;
  uploadError.value = null;
  uploadSuccess.value = false;

  if (!selectedFile.value) {
    uploadError.value = '请选择一个文件';
    uploading.value = false;
    return;
  }

  const token = localStorage.getItem('token');
  if (!token) {
    uploadError.value = '侠士尚未入世，无法上传头像。';
    uploading.value = false;
    return;
  }

  const formData = new FormData();
  formData.append('avatar', selectedFile.value);

  const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

  try {
    const response = await axios.post(`${API_BASE_URL}/api/auth/avatar`, formData, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    if (response.data && response.data.success) {
      uploadSuccess.value = true;
      if (response.data.avatarPath || response.data.avatarUrl) {
         userStore.updateAvatar(response.data.avatarPath || response.data.avatarUrl);
      }
      console.log('头像上传成功:', response.data);
    } else {
      uploadError.value = response.data.message || '头像上传失败';
    }
  } catch (err) {
    console.error('头像上传错误:', err);
    uploadError.value = err.response?.data?.message || '网络或服务器错误';
  } finally {
    uploading.value = false;
    selectedFile.value = null;
  }
};

// 保存个人资料，包括主题
const saveProfile = async () => {
  profileUpdateError.value = null;
  profileUpdateSuccess.value = false;

  const updates = {
    nickname: editableNickname.value,
    position: editablePosition.value,
    signature: editableSignature.value,
    // **新增：将主题添加到更新对象中**
    preferences: {
      theme: selectedThemeId.value
    }
  };

  const result = await userStore.updateUserInfo(updates);
  if (result.success) {
    profileUpdateSuccess.value = true;
    editingProfile.value = false;
    setTimeout(() => {
      profileUpdateSuccess.value = false;
    }, 3000);
  } else {
    profileUpdateError.value = result.message;
  }
};

// **新增：处理主题选择变化**
const handleThemeChange = (event) => {
  selectedThemeId.value = event.target.value;
  // 实时保存主题，或者在点击“保存资料”时统一保存
  // 考虑到用户体验，实时更新会更好，但这里为了简化，先统一在保存资料时更新。
  // 如果需要实时更新，可以单独抽离一个 saveTheme 函数，只调用 updateUserInfo({ preferences: { theme: newTheme } })
};


onMounted(() => {
  if (!userStore.userInfo && !userStore.loading) {
    userStore.fetchUserInfo();
  }
});

</script>

<template>
  <div class="workspace-page ancient-pixel-container" :class="currentThemeClass">
    <h1 class="page-title">我的工位 · 洞府</h1>

    <div v-if="loading" class="loading-state">
      洞府信息载入中，请稍候...
    </div>

    <div v-else-if="error" class="error-state">
      错误：{{ error }}
    </div>

    <div v-else-if="userData" class="user-info-card card">

      <div class="avatar">
        <input
          type="file"
          ref="avatarFileInput"
          id="avatarFile"
          @change="handleFileChange"
          accept="image/*"
          style="display: none;"
        />
        <img
          :src="userStore.fullAvatarUrl"
          alt="用户头像"
          class="user-avatar"
          @click="triggerFileInput"
        />
        <div v-if="selectedFile" class="upload-status-text">待上传: {{ selectedFile.name }}</div>
        <div v-if="uploading" class="upload-status-text">上传中...</div>
        <div v-if="uploadSuccess" class="upload-status-text success">上传成功!</div>
        <div v-if="uploadError" class="upload-status-text error">{{ uploadError }}</div>
        <button
           v-if="selectedFile && !uploading"
           @click.stop="uploadAvatar"
           class="pixel-button primary upload-button"
        >
          上传洞府画像
        </button>
      </div>

      <div class="details">
        <h2 class="user-main-name">{{ userData.username }}</h2>
        <p class="user-email">法号: {{ userData.email }}</p>
        <p class="user-join-date">入世时间: {{ new Date(userData.createdAt).toLocaleDateString() }}</p>

        <!-- 资料编辑区 -->
        <div v-if="editingProfile" class="edit-profile-form">
            <div class="form-group">
                <label for="nickname">江湖名号:</label>
                <input type="text" id="nickname" v-model="editableNickname" class="pixel-input" />
            </div>
            <div class="form-group">
                <label for="position">当前位阶:</label>
                <input type="text" id="position" v-model="editablePosition" class="pixel-input" />
            </div>
            <div class="form-group">
                <label for="signature">江湖宣言:</label>
                <textarea id="signature" v-model="editableSignature" rows="3" class="pixel-input"></textarea>
            </div>
            <!-- **新增：洞府主题选择** -->
            <div class="form-group">
                <label for="theme">洞府主题:</label>
                <select id="theme" v-model="selectedThemeId" @change="handleThemeChange" class="pixel-input">
                    <option v-for="theme in availableThemes" :key="theme.id" :value="theme.id">
                        {{ theme.icon }} {{ theme.name }}
                    </option>
                </select>
            </div>
            <!-- 资料更新错误和成功提示 -->
            <div v-if="profileUpdateError" class="error-message shaking-pixel">{{ profileUpdateError }}</div>
            <div v-if="profileUpdateSuccess" class="success-message">资料更新成功！</div>
            <div class="profile-actions">
              <button @click="saveProfile" class="pixel-button primary">保存资料</button>
              <button @click="editingProfile = false" class="pixel-button secondary cancel-button">取消</button>
            </div>
        </div>
        <!-- 资料显示区 -->
        <div v-else class="display-profile">
            <p>江湖名号: <span>{{ userData.nickname || '未设置' }}</span></p>
            <p>当前位阶: <span>{{ userData.position || '未设置' }}</span></p>
            <p>江湖宣言: <span>{{ userData.signature || '未设置' }}</span></p>
            <!-- **新增：显示当前洞府主题** -->
            <p>洞府主题: <span>{{ availableThemes.find(t => t.id === selectedThemeId)?.name || '未设置' }}</span></p>
            <button @click="editingProfile = true" class="pixel-button primary edit-button">编辑资料</button>
        </div>
      </div>
    </div>

    <div v-else class="no-data-state">
      未能加载侠士洞府信息。
    </div>


    <div class="workspace-sections">

        <div class="card">
            <h3>近期江湖历练</h3>
            <p>暂无近期历练信息。</p>
            <p>（待开发）</p>
        </div>

         <div class="card">
            <h3>洞府秘藏</h3>
            <ul>
                <li><a href="#">摸鱼功法研习录</a></li>
                <li><a href="#">江湖声望排行碑</a></li>
            </ul>
        </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
@use '../styles/ancient-pixel.scss';

.workspace-page {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  min-height: calc(100vh - 60px);
  font-family: 'Pixelify Sans', monospace;
  color: var(--color-ancient-dark-brown);
  // **基础背景色，会被主题背景图覆盖**
  background-color: var(--color-ancient-paper);
  background-repeat: repeat; // Default repeat for tiling patterns
  background-position: center center;
  background-size: auto; // Default size

  // **新增：洞府主题样式**
  &.theme-default {
    background-image: url('/backgrounds/bg_default.png'); // 假定图片在 public/backgrounds/
    background-size: 120px 120px; // 小方块重复
    image-rendering: pixelated;
  }
  &.theme-forest {
    background-image: url('/backgrounds/bg_forest.png');
    background-size: cover; // 覆盖整个区域
    background-position: bottom center;
    image-rendering: pixelated;
  }
  &.theme-cave {
    background-image: url('/backgrounds/bg_cave.png');
    background-size: cover;
    background-position: center center;
    image-rendering: pixelated;
  }
  &.theme-ancient-hall {
    background-image: url('/backgrounds/bg_ancient_hall.png');
    background-size: cover;
    background-position: center center;
    image-rendering: pixelated;
  }
}

.page-title {
  font-family: 'ZCOOL KuaiLe', serif;
  font-size: 2em;
  color: var(--color-ancient-ink);
  margin-bottom: 10px;
  align-self: flex-start;
}

.loading-state,
.error-state,
.no-data-state {
  text-align: center;
  padding: 20px;
  background-color: var(--color-ancient-paper);
  border: 2px solid var(--color-ancient-dark-brown);
  border-radius: 0;
  box-shadow: 4px 4px 0px var(--color-ancient-light-brown);
  color: var(--color-ancient-dark-brown);
}

.error-state {
    color: #c62828;
    background-color: #ffebee;
    border: 1px solid #ef9a9a;
    font-family: 'Pixelify Sans', monospace;
}

.user-info-card.card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 25px;
  background-color: var(--color-ancient-paper);
  border-radius: 0;
  border: 3px solid var(--color-ancient-dark-brown);
  box-shadow: 8px 8px 0px var(--color-ancient-light-brown);
  width: 100%;
  max-width: 600px;
}

.avatar {
    position: relative;
    width: 100px;
    height: 100px;
    border-radius: 0;
    border: 3px solid var(--color-ancient-dark-brown);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    cursor: pointer;
    background-color: var(--color-ancient-light-brown);
    flex-shrink: 0;
    image-rendering: pixelated;

    .user-avatar {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .upload-status-text {
        position: absolute;
        bottom: 5px;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 11px;
        font-family: 'Pixelify Sans', monospace;
        color: var(--color-ancient-ink);
        background-color: rgba(255, 255, 255, 0.7);
        padding: 2px 5px;
        border-radius: 0;
        z-index: 5;
    }

    .upload-status-text.success {
        color: var(--color-ancient-jade);
    }

    .upload-status-text.error {
        color: var(--color-ancient-blood-red);
    }

    .upload-button {
      margin-top: 5px;
      font-size: 0.8em;
      padding: 5px 10px;
      border-width: 1px;
      box-shadow: 2px 2px 0px var(--color-ancient-light-brown);
      z-index: 5;
      &:hover {
        transform: translate(-1px, -1px);
        box-shadow: 3px 3px 0px var(--color-ancient-light-brown);
      }
      &:active {
        transform: translate(1px, 1px);
        box-shadow: 1px 1px 0px var(--color-ancient-light-brown);
      }
    }
}

.details {
    flex-grow: 1;

    h2.user-main-name {
        font-family: 'ZCOOL KuaiLe', serif;
        font-size: 1.5em;
        color: var(--color-ancient-ink);
        margin: 0 0 5px 0;
    }

    p {
        font-family: 'Noto Serif SC', serif;
        font-size: 1em;
        color: var(--color-ancient-dark-brown);
        margin: 3px 0;
    }
}


/* Profile Edit Form */
.edit-profile-form {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed var(--color-ancient-light-brown);

  .form-group {
    margin-bottom: 10px;
    label {
      display: block;
      margin-bottom: 5px;
      font-family: 'Pixelify Sans', monospace;
      font-weight: bold;
      color: var(--color-ancient-dark-brown);
      font-size: 1.1em;
    }
    input[type="text"], textarea, select {
      @extend .pixel-input;
    }
  }
  .profile-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
  }
  .cancel-button {
    background-color: var(--color-ancient-stone-gray);
    border-color: var(--color-ancient-stone-gray-dark);
    color: var(--color-neutral-white);
    &:hover {
      background-color: var(--color-ancient-stone-gray-light);
    }
  }
  .error-message, .success-message {
      @extend .error-message;
      margin-top: 10px;
      margin-bottom: 20px;
  }
  .success-message {
      border-color: var(--color-ancient-jade);
      color: var(--color-ancient-jade-dark);
      background-color: var(--color-ancient-paper);
  }
}

/* Profile Display Area */
.display-profile {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed var(--color-ancient-light-brown);
  p {
    font-family: 'Noto Serif SC', serif;
    font-size: 0.95em;
    margin-bottom: 8px;
    color: var(--color-ancient-dark-brown);
    span {
      font-weight: bold;
      color: var(--color-ancient-ink);
    }
  }
  .edit-button {
    margin-top: 10px;
  }
}


.workspace-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    width: 100%;
    max-width: 600px;
}

.workspace-sections .card {
    padding: 20px;
    background-color: var(--color-ancient-paper);
    border-radius: 0;
    border: 2px solid var(--color-ancient-dark-brown);
    box-shadow: 6px 6px 0px var(--color-ancient-light-brown);
}

.workspace-sections .card h3 {
    font-family: 'ZCOOL KuaiLe', serif;
    font-size: 1.3em;
    color: var(--color-ancient-ink);
    margin: 0 0 10px 0;
    border-bottom: 1px dashed var(--color-ancient-light-brown);
    padding-bottom: 5px;
}

.workspace-sections .card p,
.workspace-sections .card ul {
     font-family: 'Noto Serif SC', serif;
     font-size: 0.9em;
     color: var(--color-ancient-dark-brown);
     margin: 0;
     padding: 0;
}

.workspace-sections .card ul {
    list-style: none;
}

.workspace-sections .card ul li {
    margin-bottom: 5px;
}

.workspace-sections .card ul li a {
     color: var(--color-ancient-jade);
     text-decoration: none;

     &:hover {
         text-decoration: underline;
     }
}

// Responsive adjustments
@media (max-width: 600px) {
  .user-info-card.card {
    flex-direction: column;
    text-align: center;
  }

  .avatar {
      margin-bottom: 10px;
  }

  .details {
      text-align: center;
  }

   .workspace-sections {
       grid-template-columns: 1fr;
   }
}
</style>
```

**2.2. `frontend/public/backgrounds/` (新增目录和图片)**

为了让前端能显示不同的主题背景，你需要在 `frontend/public/` 目录下创建一个 `backgrounds` 文件夹，并放入对应的像素风图片。

**`frontend/public/backgrounds/bg_default.png`** (示例，你可以用任何像素平铺图)
![Default Background Example](https://via.placeholder.com/120/E8D6B7/000000?text=DefaultBG)

**`frontend/public/backgrounds/bg_forest.png`** (示例，森林主题背景)
![Forest Background Example](https://via.placeholder.com/800x600/7AA87C/FFFFFF?text=ForestBG)

**`frontend/public/backgrounds/bg_cave.png`** (示例，洞穴主题背景)
![Cave Background Example](https://via.placeholder.com/800x600/5A5A5A/FFFFFF?text=CaveBG)

**`frontend/public/backgrounds/bg_ancient_hall.png`** (示例，殿宇主题背景)
![Ancient Hall Background Example](https://via.placeholder.com/800x600/D2B48C/000000?text=HallBG)

*请注意：上述图片链接是占位符，你需要实际制作或寻找符合像素古风武侠风格的图片放入 `frontend/public/backgrounds/` 目录下。*

---

**测试与验证步骤：**

1.  **准备主题图片：** 确保你已经在 `frontend/public/backgrounds/` 目录中放置了 `bg_default.png`, `bg_forest.png`, `bg_cave.png`, `bg_ancient_hall.png` 这些图片。
2.  **启动后端：**
    *   进入 `backend` 目录。
    *   运行 `npm install`。
    *   运行 `npm run dev` 启动后端服务器。
3.  **启动前端：**
    *   进入 `frontend` 目录。
    *   运行 `npm install`。
    *   运行 `npm run dev` 启动前端应用。
4.  **登录应用：**
    *   在浏览器中访问 `http://localhost:5173` 并登录。
5.  **测试洞府主题：**
    *   导航到“我的工位” (`/workspace`) 页面。
    *   确认页面背景显示为默认主题 (`bg_default.png` 如果你放置了)。
    *   点击“编辑资料”按钮。
    *   在弹出的编辑表单中，找到“洞府主题”下拉框。
    *   选择一个新主题（例如“翠竹幽林”）。
    *   点击“保存资料”按钮。
    *   观察页面，背景应该立即切换到新选择的主题背景图。
    *   刷新页面，确认主题选择仍然被保留，并且背景是正确的。
    *   尝试切换其他主题，并重复保存和刷新，验证所有主题都能正确显示和持久化。

通过这个迭代，用户现在可以初步定制自己的“洞府”背景，这大大增强了项目的沉浸感和个性化体验！