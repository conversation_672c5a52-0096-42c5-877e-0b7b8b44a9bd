:root {
  /* Design System Colors - New for Pixel/Ancient Martial Arts Style */
  --color-ancient-paper: #F8E7D5; /* 古朴的纸张色 */
  --color-ancient-dark-brown: #5A3F2B; /* 深棕色，用于主要文字和边框 */
  --color-ancient-light-brown: #8B6E5B; /* 浅棕色，用于辅助文字 */
  --color-ancient-ink: #2C2F33; /* 墨色，用于重要标题或按钮文字 */
  --color-ancient-jade: #4CAF50; /* 翡翠绿，用于成功/点缀 */
  --color-ancient-gold: #FFD700; /* 黄金色，用于奖励/高亮 */
  --color-ancient-blood-red: #D22B2B; /* 血红色，用于警告/重要提示 */
  --color-ancient-stone-gray: #B0B0B0; /* 石头灰，用于禁用状态 */
  --color-ancient-stone-gray-dark: #9A9A9A; /* 石头灰的深色版本 */
  --color-ancient-stone-gray-light: #C0C0C0; /* 石头灰的浅色版本 */
  --color-ancient-jade-dark: #449948; /* 翡翠绿的深色版本 */
  --color-ancient-jade-light: #5CBF60; /* 翡翠绿的浅色版本 */
  --color-ancient-blood-red-dark: #BD2525; /* 血红色的深色版本 */
  --color-ancient-blood-red-light: #E83535; /* 血红色的浅色版本 */
  --color-ancient-highlight: #AEE5A9; /* 薄荷绿作为高亮 */
  --color-ancient-gold-light: #FFE44D; /* 比原来的金色更亮 */
  --color-neutral-white: #FFFFFF; /* 纯白色 */

  /* Default font settings - Using new fonts */
  font-family: "ZCOOL KuaiLe", "Noto Serif SC", serif, "Pixelify Sans", monospace, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.6; /* 适当增加行高 */
  font-weight: 400;

  /* Using ancient colors defined above */
  color: var(--color-ancient-dark-brown); /* 主要文字颜色 */
  background-color: var(--color-ancient-paper); /* 页面背景色 */

  font-synthesis: none;
  text-rendering: pixelated;
  -webkit-font-smoothing: none;
  -moz-osx-font-smoothing: none;
  -webkit-text-size-adjust: 100%;
}

/* Global Reset */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  image-rendering: optimizeSpeed; /* Older versions of FF */
  image-rendering: -moz-crisp-edges; /* FF 6.0+ */
  image-rendering: -webkit-optimize-contrast; /* Safari, Chrome, Edge 79+ */
  image-rendering: optimize-contrast; /* CSS3 */
  image-rendering: pixelated; /* Chrome 41+, Opera 26+, Safari 9+ */
  -ms-interpolation-mode: nearest-neighbor; /* IE 9+ */
}

/* Links */
a {
  font-weight: 700; /* 加粗 */
  color: var(--color-ancient-jade); /* Links color based on ancient jade */
  text-decoration: none; /* 移除下划线 */
  transition: color 0.2s ease-in-out;
}
a:hover {
  color: var(--color-ancient-jade-dark); /* Link hover color */
  text-decoration: underline; /* 悬停时显示下划线 */
}

a:hover {
  color: var(--pixel-secondary);
  text-shadow: 2px 2px 0 var(--pixel-dark);
}

/* Body */
body {
  margin: 0;
  background-color: var(--color-ancient-paper);
  min-width: 320px;
  min-height: 100vh;
  background-color: var(--pixel-white);
  background-image: var(--scroll-bg);
  image-rendering: optimizeSpeed; /* Older versions of FF */
  image-rendering: -moz-crisp-edges; /* FF 6.0+ */
  image-rendering: -webkit-optimize-contrast; /* Safari, Chrome, Edge 79+ */
  image-rendering: optimize-contrast; /* CSS3 */
  image-rendering: pixelated; /* Chrome 41+, Opera 26+, Safari 9+ */
  -ms-interpolation-mode: nearest-neighbor; /* IE 9+ */
}

/* Headings */
h1 {
  font-size: 3em; /* 增大标题 */
  line-height: 1.2;
  color: var(--color-ancient-ink); /* 墨色标题 */
  font-family: "ZCOOL KuaiLe", "Noto Serif SC", serif; /* 标题使用古风字体 */
  font-weight: 700;
}

h2, h3, h4, h5, h6 {
  font-family: "ZCOOL KuaiLe", "Noto Serif SC", serif;
  color: var(--color-ancient-dark-brown);
}

h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--pixel-primary);
  image-rendering: optimizeSpeed; /* Older versions of FF */
  image-rendering: -moz-crisp-edges; /* FF 6.0+ */
  image-rendering: -webkit-optimize-contrast; /* Safari, Chrome, Edge 79+ */
  image-rendering: optimize-contrast; /* CSS3 */
  image-rendering: pixelated; /* Chrome 41+, Opera 26+, Safari 9+ */
  -ms-interpolation-mode: nearest-neighbor; /* IE 9+ */
}

/* Buttons */
button {
  /* Base button style for pixel art look */
  border-radius: 0; /* Sharp corners for pixel style */
  border: 2px solid var(--color-ancient-dark-brown); /* 像素化边框 */
  padding: 0.8em 1.5em; /* 增加内边距 */
  font-size: 1em;
  font-weight: 700; /* 更粗的字体 */
  font-family: "Pixelify Sans", monospace; /* 按钮文字使用像素字体 */
  background-color: var(--color-ancient-gold); /* 默认按钮背景色：金色 */
  color: var(--color-ancient-ink); /* 默认按钮文字颜色：墨色 */
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s, border-color 0.2s, transform 0.1s;
  box-shadow: 4px 4px 0px var(--color-ancient-light-brown); /* 像素风格阴影 */

  &:hover:not(:disabled) {
    background-color: var(--color-ancient-gold-light);
    transform: translate(-2px, -2px); /* 悬停时向上和左移动，模拟像素游戏按钮效果 */
    box-shadow: 6px 6px 0px var(--color-ancient-light-brown);
  }
  &:active:not(:disabled) {
    transform: translate(2px, 2px); /* 点击时向下和右移动 */
    box-shadow: 2px 2px 0px var(--color-ancient-light-brown);
  }
  &:focus,
  &:focus-visible {
    outline: none; /* 移除默认焦点轮廓 */
    border-color: var(--color-ancient-jade); /* 焦点时边框变色 */
  }
  &:disabled {
    background-color: var(--color-ancient-stone-gray);
    color: var(--color-neutral-white);
    border-color: var(--color-ancient-stone-gray-dark);
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
    opacity: 0.8; /* 禁用状态略微透明 */
  }
}

button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: 0.5s steps(4);
}

button:hover {
  background-color: var(--pixel-primary);
  color: var(--pixel-white);
}

button:hover::before {
  left: 100%;
}

button:active {
  transform: translate(2px, 2px);
  box-shadow: 2px 2px 0 var(--pixel-dark);
}

/* Cards */
.card {
  padding: 2em;
  background-color: var(--color-ancient-paper); /* 纸张色 */
  border-radius: 0; /* 锐利边缘 */
  border: 3px solid var(--color-ancient-dark-brown); /* 粗边框 */
  box-shadow: 8px 8px 0px var(--color-ancient-light-brown); /* 更明显的像素阴影 */
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: repeating-linear-gradient(
    90deg,
    var(--pixel-primary),
    var(--pixel-primary) 8px,
    transparent 8px,
    transparent 16px
  );
}

/* App Container */
#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0;
  text-align: center; /* Adjust as needed per page */
}

/* Helper for pixelated images if needed */
.pixelated-image {
  image-rendering: optimizeSpeed; /* Older versions of FF */
  image-rendering: -moz-crisp-edges; /* FF 6.0+ */
  image-rendering: -webkit-optimize-contrast; /* Safari, Chrome, Edge 79+ */
  image-rendering: optimize-contrast; /* CSS3 */
  image-rendering: pixelated; /* Chrome 41+, Opera 26+, Safari 9+ */
  -ms-interpolation-mode: nearest-neighbor; /* IE 9+ */
}

/* Input styles */
.pixel-input {
  width: 100%;
  padding: 8px 12px;
  border: 2px solid var(--color-ancient-dark-brown);
  border-radius: 0; /* 锐利边缘 */
  background-color: var(--color-ancient-paper);
  color: var(--color-ancient-ink);
  font-family: 'Pixelify Sans', monospace;
  font-size: 0.9em;
  box-shadow: 2px 2px 0px var(--color-ancient-light-brown);
  transition: all 0.2s ease;
}

.pixel-input:focus {
  outline: none;
  border-color: var(--color-ancient-jade);
  box-shadow: 3px 3px 0px var(--color-ancient-light-brown);
  transform: translate(-1px, -1px);
}

.pixel-input::placeholder {
  color: var(--color-ancient-stone-gray);
  font-style: italic;
}

/* Ancient pixel container style */
.ancient-pixel-container {
  background-color: var(--color-ancient-paper);
  border: 3px solid var(--color-ancient-dark-brown);
  border-radius: 0;
  box-shadow: 8px 8px 0px var(--color-ancient-light-brown);
  padding: 25px;
  margin: 20px;
  position: relative;
}

/* Scrollbars */
::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

::-webkit-scrollbar-track {
  background: var(--color-ancient-paper);
  border: 1px solid var(--color-ancient-dark-brown);
}

::-webkit-scrollbar-thumb {
  background: var(--color-ancient-light-brown);
  border: 1px solid var(--color-ancient-dark-brown);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-ancient-dark-brown);
}

/* 数字和时间显示专用样式 - 确保可读性 */
.number-display, .time-display {
  font-family: 'Noto Serif SC', serif, 'Arial', sans-serif !important;
  font-weight: bold;
  letter-spacing: 0.5px;
}

/* 像素风格但保持数字可读性的混合样式 */
.pixel-text-with-numbers {
  font-family: 'ZCOOL KuaiLe', serif;
}

.pixel-text-with-numbers .number,
.pixel-text-with-numbers .time {
  font-family: 'Noto Serif SC', serif, 'Arial', sans-serif;
  font-weight: bold;
}
